import { getCurrentUser } from '@/lib/auth';
import { supabase } from '@/lib/supabase';
import { notFound } from 'next/navigation';
import { QAPairsView } from '@/components/qa-pairs/qa-pairs-view';

interface CollectionPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function CollectionPage({ params }: CollectionPageProps) {
  const { id } = await params;
  const user = await getCurrentUser();

  if (!user?.id) {
    notFound();
  }

  // Fetch the collection and verify ownership
  const { data: collection, error: collectionError } = await supabase
    .from('collections')
    .select('*')
    .eq('id', id)
    .eq('user_id', user.id)
    .single();

  if (collectionError || !collection) {
    notFound();
  }

  // Fetch Q&A pairs for this collection
  const { data: qaPairs, error: qaPairsError } = await supabase
    .from('qa_pairs')
    .select('*')
    .eq('collection_id', id)
    .order('created_at', { ascending: false });

  if (qaPairsError) {
    console.error('Error fetching Q&A pairs:', qaPairsError);
  }

  try {
    return (
      <QAPairsView
        collection={collection}
        qaPairs={qaPairs || []}
      />
    );
  } catch (error) {
    console.error('Error rendering QAPairsView:', error);
    return (
      <div>
        <h1>Error loading collection</h1>
        <p>There was an error loading this collection. Please try again.</p>
      </div>
    );
  }
}
