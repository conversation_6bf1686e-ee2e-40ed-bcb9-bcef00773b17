'use client';

import { useRouter, usePathname } from 'next/navigation';
import { signOut } from 'next-auth/react';
import {
  AppShell,
  Text,
  UnstyledButton,
  Group,
  Avatar,
  Menu,
  rem,
  Burger,
  ScrollArea,
  NavLink,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  IconSection,
  IconHome,
  IconLogout,
  IconSettings,
  IconChevronDown,
  IconServer,
} from '@tabler/icons-react';
import { APP_CONFIG } from '@/lib/config';

interface DashboardShellProps {
  children: React.ReactNode;
  user: {
    name?: string | null;
    email?: string | null;
    image?: string | null;
  };
}

const navigation = [
  { label: 'Dashboard', href: '/dashboard', icon: IconHome },
  { label: 'Collections', href: '/dashboard/collections', icon: IconSection },
  { label: 'System Status', href: '/dashboard/status', icon: IconServer },
];

export function DashboardShell({ children, user }: DashboardShellProps) {
  const [opened, { toggle }] = useDisclosure();
  const router = useRouter();
  const pathname = usePathname();

  const handleSignOut = async () => {
    await signOut({ callbackUrl: '/' });
  };

  return (
    <AppShell
      header={{ height: 60 }}
      navbar={{
        width: 300,
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="md"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Burger opened={opened} onClick={toggle} hiddenFrom="sm" size="sm" />
            <Text size="xl" fw={700} c="blue">
              {APP_CONFIG.name}
            </Text>
          </Group>

          <Menu shadow="md" width={200}>
            <Menu.Target>
              <UnstyledButton>
                <Group gap="xs">
                  <Avatar src={user.image} size="sm" radius="xl" />
                  <div style={{ flex: 1 }}>
                    <Text size="sm" fw={500}>
                      {user.name}
                    </Text>
                    <Text c="dimmed" size="xs">
                      {user.email}
                    </Text>
                  </div>
                  <IconChevronDown size={rem(14)} />
                </Group>
              </UnstyledButton>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item
                leftSection={<IconSettings size={rem(14)} />}
                onClick={() => router.push('/dashboard/settings')}
              >
                Settings
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item
                leftSection={<IconLogout size={rem(14)} />}
                onClick={handleSignOut}
              >
                Sign out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar p="md">
        <AppShell.Section grow component={ScrollArea}>
          <div>
            {navigation.map((item) => (
              <NavLink
                key={item.href}
                href={item.href}
                label={item.label}
                leftSection={<item.icon size="1rem" />}
                active={pathname === item.href}
                onClick={() => router.push(item.href)}
                style={{ borderRadius: '8px', marginBottom: '4px' }}
              />
            ))}
          </div>
        </AppShell.Section>
      </AppShell.Navbar>

      <AppShell.Main>{children}</AppShell.Main>
    </AppShell>
  );
}
