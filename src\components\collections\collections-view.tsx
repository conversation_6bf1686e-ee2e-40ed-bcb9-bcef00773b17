'use client';

import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import {
  Title,
  Text,
  Button,
  Card,
  Group,
  Stack,
  SimpleGrid,
  ActionIcon,
  Menu,
  Badge,
  Modal,
  TextInput,
  Textarea,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useForm } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import {
  IconPlus,
  IconDots,
  IconEdit,
  IconTrash,
  IconSection,
} from '@tabler/icons-react';
import { createCollection, updateCollection, deleteCollection } from '@/lib/actions/collections';

interface Collection {
  id: string;
  name: string;
  description?: string;
  user_id: string;
  created_at: string;
  qa_pairs?: { count: number }[];
}

interface CollectionsViewProps {
  collections: Collection[];
  userId: string;
}

export function CollectionsView({ collections: initialCollections, userId }: CollectionsViewProps) {
  const [collections, setCollections] = useState(initialCollections);
  const [opened, { open, close }] = useDisclosure(false);
  const [editingCollection, setEditingCollection] = useState<Collection | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();

  const form = useForm({
    initialValues: {
      name: '',
      description: '',
    },
    validate: {
      name: (value) => (value.trim().length < 1 ? 'Name is required' : null),
    },
  });

  // Check if we should open the create modal from URL params
  useState(() => {
    if (searchParams.get('action') === 'create') {
      open();
    }
  });

  const handleSubmit = async (values: { name: string; description: string }) => {
    setLoading(true);
    try {
      if (editingCollection) {
        // Update existing collection
        const result = await updateCollection(editingCollection.id, values);
        if (result.success) {
          setCollections(prev => 
            prev.map(c => c.id === editingCollection.id 
              ? { ...c, ...values } 
              : c
            )
          );
          notifications.show({
            title: 'Success',
            message: 'Collection updated successfully',
            color: 'green',
          });
        } else {
          throw new Error(result.error);
        }
      } else {
        // Create new collection
        const result = await createCollection({ ...values, user_id: userId });
        if (result.success && result.data) {
          setCollections(prev => [result.data, ...prev]);
          notifications.show({
            title: 'Success',
            message: 'Collection created successfully',
            color: 'green',
          });
        } else {
          throw new Error(result.error);
        }
      }
      
      handleCloseModal();
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Something went wrong',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = (collection: Collection) => {
    setEditingCollection(collection);
    form.setValues({
      name: collection.name,
      description: collection.description || '',
    });
    open();
  };

  const handleDelete = async (collection: Collection) => {
    if (!confirm(`Are you sure you want to delete "${collection.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const result = await deleteCollection(collection.id);
      if (result.success) {
        setCollections(prev => prev.filter(c => c.id !== collection.id));
        notifications.show({
          title: 'Success',
          message: 'Collection deleted successfully',
          color: 'green',
        });
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: error instanceof Error ? error.message : 'Failed to delete collection',
        color: 'red',
      });
    }
  };

  const handleCloseModal = () => {
    close();
    setEditingCollection(null);
    form.reset();
    // Clear URL params
    router.replace('/dashboard/collections');
  };

  return (
    <>
      <Stack gap="lg">
        <Group justify="space-between">
          <div>
            <Title order={1}>Collections</Title>
            <Text c="dimmed">Organize your Q&A pairs into collections</Text>
          </div>
          <Button leftSection={<IconPlus size="1rem" />} onClick={open}>
            New Collection
          </Button>
        </Group>

        {collections.length === 0 ? (
          <Card shadow="sm" padding="xl" radius="md" withBorder>
            <Stack align="center" gap="md">
              <IconSection size={48} color="var(--mantine-color-dimmed)" />
              <div style={{ textAlign: 'center' }}>
                <Text size="lg" fw={500}>No collections yet</Text>
                <Text c="dimmed">Create your first collection to get started</Text>
              </div>
              <Button leftSection={<IconPlus size="1rem" />} onClick={open}>
                Create Collection
              </Button>
            </Stack>
          </Card>
        ) : (
          <SimpleGrid cols={{ base: 1, sm: 2, lg: 3 }} spacing="lg">
            {collections.map((collection) => (
              <Card key={collection.id} shadow="sm" padding="lg" radius="md" withBorder>
                <Group justify="space-between" mb="xs">
                  <Text fw={500} truncate style={{ flex: 1 }}>
                    {collection.name}
                  </Text>
                  <Menu shadow="md" width={200}>
                    <Menu.Target>
                      <ActionIcon variant="subtle" color="gray">
                        <IconDots size="1rem" />
                      </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<IconEdit size="0.9rem" />}
                        onClick={() => handleEdit(collection)}
                      >
                        Edit
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<IconTrash size="0.9rem" />}
                        color="red"
                        onClick={() => handleDelete(collection)}
                      >
                        Delete
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Group>

                {collection.description && (
                  <Text size="sm" c="dimmed" mb="md" lineClamp={3}>
                    {collection.description}
                  </Text>
                )}

                <Group justify="space-between" mt="auto">
                  <Group gap="xs">
                    <Badge variant="light" size="sm">
                      {new Date(collection.created_at).toLocaleDateString()}
                    </Badge>
                    <Badge variant="outline" size="sm" color="blue">
                      {collection.qa_pairs?.[0]?.count || 0} Q&As
                    </Badge>
                  </Group>
                  <Button
                    variant="light"
                    size="xs"
                    onClick={() => router.push(`/dashboard/collections/${collection.id}`)}
                  >
                    View Q&As
                  </Button>
                </Group>
              </Card>
            ))}
          </SimpleGrid>
        )}
      </Stack>

      <Modal
        opened={opened}
        onClose={handleCloseModal}
        title={editingCollection ? 'Edit Collection' : 'Create New Collection'}
        size="md"
      >
        <form onSubmit={form.onSubmit(handleSubmit)}>
          <Stack gap="md">
            <TextInput
              label="Name"
              placeholder="Enter collection name"
              required
              {...form.getInputProps('name')}
            />
            <Textarea
              label="Description"
              placeholder="Enter collection description (optional)"
              rows={3}
              {...form.getInputProps('description')}
            />
            <Group justify="flex-end" gap="sm">
              <Button variant="subtle" onClick={handleCloseModal}>
                Cancel
              </Button>
              <Button type="submit" loading={loading}>
                {editingCollection ? 'Update' : 'Create'}
              </Button>
            </Group>
          </Stack>
        </form>
      </Modal>
    </>
  );
}
